/**
 * Cloudflare Functions - 根据键获取值 API 端点
 * 
 * 功能说明：
 * - 接收 HTTP 请求，从请求中提取键(key)参数
 * - 调用 Cloudflare KV 存储服务获取存储的数据
 * - 返回 JSON 格式的响应，包含获取的值
 * - 支持 CORS 跨域访问
 * 
 * 请求方式：GET
 * 请求参数：
 * - key: 要获取的键名（字符串）
 * 
 * 响应格式：
 * {
 *   "success": true/false,
 *   "message": "操作结果描述",
 *   "key": "请求的键名",
 *   "value": "存储的值",
 *   "found": true/false,
 *   "timestamp": "操作时间戳"
 * }
 */

export async function onRequest({ request, params, env }) {
  try {
    // 设置 CORS 响应头
    const corsHeaders = {
      'Content-Type': 'application/json; charset=UTF-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // 处理 OPTIONS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders,
      });
    }

    // 只允许 GET 请求
    if (request.method !== 'GET') {
      return new Response(JSON.stringify({
        success: false,
        message: '仅支持 GET 请求方法',
        error: 'METHOD_NOT_ALLOWED'
      }), {
        status: 405,
        headers: corsHeaders,
      });
    }

    // 从 URL 查询参数中提取键名
    const url = new URL(request.url);
    const key = url.searchParams.get('key');

    // 验证必需参数
    if (!key) {
      return new Response(JSON.stringify({
        success: false,
        message: '缺少必需参数：key（键名）',
        error: 'MISSING_KEY_PARAMETER'
      }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // 调用 Cloudflare KV 存储服务获取数据
    // 使用环境变量中的 KV 命名空间，通常命名为 STORAGE 或 KV_STORAGE
    const kvNamespace = env.STORAGE || env.KV_STORAGE || env.edgeOneStorage;

    if (!kvNamespace) {
      throw new Error('KV 存储命名空间未配置，请检查环境变量设置');
    }

    const value = await kvNamespace.get(key);

    // 检查是否找到数据
    const found = value !== null && value !== undefined;
    
    let parsedValue = value;
    
    // 尝试解析 JSON 格式的值
    if (found && typeof value === 'string') {
      try {
        parsedValue = JSON.parse(value);
      } catch (e) {
        // 如果不是有效的 JSON，保持原始字符串值
        parsedValue = value;
      }
    }

    // 返回响应
    const response = {
      success: true,
      message: found ? '数据获取成功' : '未找到指定键的数据',
      key: key,
      value: parsedValue,
      found: found,
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    });

  } catch (error) {
    // 错误处理
    console.error('获取数据时发生错误:', error);
    
    const errorResponse = {
      success: false,
      message: '获取数据时发生内部错误',
      error: error.message || 'INTERNAL_SERVER_ERROR',
      key: new URL(request.url).searchParams.get('key') || 'unknown',
      found: false,
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}
