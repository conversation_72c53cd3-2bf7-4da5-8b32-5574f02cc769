/**
 * Split-Second Spark - 存储配置
 * 定义各种存储方案的配置选项
 */

/**
 * 存储配置类
 */
class StorageConfig {
    constructor() {
        this.configs = {
            // 基础本地存储配置
            local: {
                dbName: 'SplitSecondSparkDB',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['indexeddb', 'localstorage', 'memory']
            },

            // Firebase 云存储配置
            firebase: {
                dbName: 'SplitSecondSparkDB',
                dbVersion: 1,
                enableCloudSync: true,
                syncInterval: 5 * 60 * 1000, // 5分钟同步一次
                adapterPriority: ['hybrid', 'indexeddb', 'localstorage', 'memory'],
                cloudConfig: {
                    type: 'firebase',
                    config: {
                        // Firebase 配置 - 需要用户提供实际的配置信息
                        apiKey: "your-api-key",
                        authDomain: "your-project.firebaseapp.com",
                        projectId: "your-project-id",
                        storageBucket: "your-project.appspot.com",
                        messagingSenderId: "123456789",
                        appId: "your-app-id"
                    }
                }
            },

            // 高性能本地存储配置
            performance: {
                dbName: 'SplitSecondSparkDB_Performance',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['indexeddb'], // 仅使用 IndexedDB 以获得最佳性能
                cacheSize: 1000, // 缓存大小
                compressionEnabled: true // 启用数据压缩
            },

            // 隐私模式配置（仅内存存储）
            privacy: {
                dbName: 'SplitSecondSparkDB_Privacy',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['memory'], // 仅使用内存存储，不保存到磁盘
                autoCleanup: true // 自动清理数据
            },

            // 开发调试配置
            debug: {
                dbName: 'SplitSecondSparkDB_Debug',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['indexeddb', 'localstorage', 'memory'],
                verboseLogging: true, // 详细日志
                debugMode: true // 调试模式
            }
        };

        // 当前使用的配置
        this.currentConfig = 'local';
        
        console.log('📋 存储配置管理器已初始化');
    }

    /**
     * 获取指定配置
     * @param {string} configName - 配置名称
     * @returns {Object} 配置对象
     */
    getConfig(configName = null) {
        const name = configName || this.currentConfig;
        
        if (!this.configs[name]) {
            console.warn(`⚠️ 配置 "${name}" 不存在，使用默认配置`);
            return this.configs.local;
        }
        
        return { ...this.configs[name] };
    }

    /**
     * 设置当前配置
     * @param {string} configName - 配置名称
     */
    setConfig(configName) {
        if (!this.configs[configName]) {
            throw new Error(`配置 "${configName}" 不存在`);
        }
        
        this.currentConfig = configName;
        console.log(`📋 已切换到配置: ${configName}`);
    }

    /**
     * 添加自定义配置
     * @param {string} name - 配置名称
     * @param {Object} config - 配置对象
     */
    addConfig(name, config) {
        this.configs[name] = { ...config };
        console.log(`📋 已添加自定义配置: ${name}`);
    }

    /**
     * 获取所有可用配置
     * @returns {Array} 配置名称列表
     */
    getAvailableConfigs() {
        return Object.keys(this.configs);
    }

    /**
     * 根据环境自动选择配置
     */
    autoSelectConfig() {
        // 首先检查是否有 EdgeOne 云存储可用（使用基础检查以避免异步）
        if (this.isEdgeOneStorageBasicallyAvailable()) {
            console.log('🌐 检测到 EdgeOne 云存储，优先使用云存储配置');
            // EdgeOne 云存储不需要特定配置，直接返回标识
            return 'edgeone';
        }

        // 检测是否在隐私模式下
        if (this.isPrivateMode()) {
            this.setConfig('privacy');
            return 'privacy';
        }

        // 检测是否在开发环境
        if (this.isDevelopmentMode()) {
            this.setConfig('debug');
            return 'debug';
        }

        // 检测是否支持高性能存储
        if (this.supportsHighPerformanceStorage()) {
            this.setConfig('performance');
            return 'performance';
        }

        // 默认使用本地存储
        this.setConfig('local');
        return 'local';
    }

    /**
     * 检测是否在隐私模式下
     */
    isPrivateMode() {
        try {
            // 尝试使用 localStorage
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            return false;
        } catch (e) {
            return true;
        }
    }

    /**
     * 检测是否在开发环境
     */
    isDevelopmentMode() {
        return location.hostname === 'localhost' || 
               location.hostname === '127.0.0.1' ||
               location.protocol === 'file:' ||
               localStorage.getItem('debug') === 'true';
    }

    /**
     * 检测是否支持高性能存储
     */
    supportsHighPerformanceStorage() {
        return 'indexedDB' in window && 
               'serviceWorker' in navigator &&
               window.performance && 
               window.performance.memory;
    }

    /**
     * 创建存储服务实例
     * @param {string} configName - 配置名称（可选）
     * @returns {Promise<StorageService>} 存储服务实例
     */
    async createStorageService(configName = null) {
        // 首先进行 EdgeOne 云存储的详细检查
        if (this.isEdgeOneStorageBasicallyAvailable()) {
            console.log('🌐 检测到 EdgeOne 云存储，进行详细验证...');

            try {
                // 执行详细的可用性检查
                const availabilityResult = await this.isEdgeOneStorageAvailable(true);

                if (availabilityResult.available) {
                    console.log('✅ EdgeOne 云存储验证通过，使用云存储方案');
                    console.log('📊 EdgeOne 存储详情:', availabilityResult.details);

                    // 使用基础存储服务，它已经支持 EdgeOne 云存储
                    const { default: StorageService } = await import('../utils/storage.js');
                    const storageService = new StorageService();
                    await storageService.init();

                    console.log('✅ EdgeOne 云存储服务已创建');
                    return storageService;
                } else {
                    console.warn('⚠️ EdgeOne 云存储验证失败:', availabilityResult.reason);
                    console.warn('📊 失败详情:', availabilityResult.details);
                    console.log('🔄 回退到本地存储配置');
                }
            } catch (error) {
                console.error('❌ EdgeOne 云存储验证过程中发生错误:', error);
                console.log('🔄 回退到本地存储配置');
            }
        }

        // 如果没有 EdgeOne 云存储，使用原有逻辑
        const config = this.getConfig(configName);

        // 根据配置选择存储服务类型
        let StorageServiceClass;

        if (config.enableCloudSync) {
            // 使用增强存储服务（支持云同步）
            const { default: EnhancedStorageService } = await import('../utils/enhanced-storage.js');
            StorageServiceClass = EnhancedStorageService;
        } else {
            // 使用基础存储服务
            const { default: StorageService } = await import('../utils/storage.js');
            StorageServiceClass = StorageService;
        }

        const storageService = new StorageServiceClass(config);
        await storageService.init();

        console.log(`✅ 存储服务已创建，配置: ${configName || this.currentConfig}`);
        return storageService;
    }

    /**
     * 获取存储方案推荐
     */
    getRecommendations() {
        const recommendations = [];

        // 最高优先级：EdgeOne 云存储
        if (this.isEdgeOneStorageBasicallyAvailable()) {
            recommendations.push({
                config: 'edgeone',
                reason: '检测到 EdgeOne 云存储服务，推荐使用云存储以获得最佳性能和数据同步',
                priority: 'highest',
                note: '建议在使用前进行详细验证'
            });
        }

        // 基于用户环境给出推荐
        if (this.isPrivateMode()) {
            recommendations.push({
                config: 'privacy',
                reason: '检测到隐私模式，推荐使用内存存储以保护隐私',
                priority: 'high'
            });
        }

        if (this.isDevelopmentMode()) {
            recommendations.push({
                config: 'debug',
                reason: '检测到开发环境，推荐使用调试配置以便调试',
                priority: 'medium'
            });
        }

        if (this.supportsHighPerformanceStorage()) {
            recommendations.push({
                config: 'performance',
                reason: '设备支持高性能存储，推荐使用性能优化配置',
                priority: 'medium'
            });
        }

        // 检测网络连接
        if (navigator.onLine && 'serviceWorker' in navigator) {
            recommendations.push({
                config: 'firebase',
                reason: '网络连接良好，可以考虑使用云存储同步数据',
                priority: 'low'
            });
        }

        // 如果没有特殊推荐，推荐默认配置
        if (recommendations.length === 0) {
            recommendations.push({
                config: 'local',
                reason: '推荐使用标准本地存储配置',
                priority: 'medium'
            });
        }

        return recommendations;
    }

    /**
     * 显示配置信息
     */
    displayConfigInfo(configName = null) {
        const config = this.getConfig(configName);
        const name = configName || this.currentConfig;
        
        console.group(`📋 存储配置信息: ${name}`);
        console.log('数据库名称:', config.dbName);
        console.log('数据库版本:', config.dbVersion);
        console.log('云同步:', config.enableCloudSync ? '启用' : '禁用');
        console.log('适配器优先级:', config.adapterPriority);
        
        if (config.cloudConfig) {
            console.log('云存储类型:', config.cloudConfig.type);
        }
        
        if (config.syncInterval) {
            console.log('同步间隔:', `${config.syncInterval / 1000}秒`);
        }
        
        console.groupEnd();
    }
}

/**
 * 存储配置管理器单例
 */
class StorageConfigManager {
    constructor() {
        if (StorageConfigManager.instance) {
            return StorageConfigManager.instance;
        }

        this.config = new StorageConfig();
        this.currentStorageService = null;
        this.currentConfigName = null;
        this.isInitialized = false;
        StorageConfigManager.instance = this;
    }

    static getInstance() {
        if (!StorageConfigManager.instance) {
            StorageConfigManager.instance = new StorageConfigManager();
        }
        return StorageConfigManager.instance;
    }

    getConfig(name) {
        return this.config.getConfig(name);
    }

    setConfig(name) {
        return this.config.setConfig(name);
    }

    async createStorageService(configName = null) {
        const selectedConfig = configName || this.autoSelectConfig();
        const storageService = await this.config.createStorageService(selectedConfig);

        // 保存当前存储服务和配置信息
        this.currentStorageService = storageService;
        this.currentConfigName = selectedConfig;
        this.isInitialized = true;

        console.log(`✅ 存储服务已创建，配置: ${selectedConfig}`);
        return storageService;
    }

    /**
     * 验证 EdgeOne 云存储并创建存储服务
     * @param {boolean} performConnectivityTest - 是否执行连接测试
     * @returns {Promise<Object>} 包含存储服务和验证结果的对象
     */
    async createStorageServiceWithEdgeOneValidation(performConnectivityTest = true) {
        const result = {
            storageService: null,
            edgeOneValidation: null,
            configUsed: null,
            success: false
        };

        try {
            // 如果检测到 EdgeOne 存储，进行详细验证
            if (this.config.isEdgeOneStorageBasicallyAvailable()) {
                console.log('🌐 检测到 EdgeOne 云存储，进行详细验证...');

                const validationResult = await this.config.isEdgeOneStorageAvailable(performConnectivityTest);
                result.edgeOneValidation = validationResult;

                if (validationResult.available) {
                    console.log('✅ EdgeOne 云存储验证通过');
                    result.configUsed = 'edgeone';
                } else {
                    console.warn('⚠️ EdgeOne 云存储验证失败，使用备用配置');
                    result.configUsed = this.autoSelectConfig();
                }
            } else {
                console.log('📝 未检测到 EdgeOne 云存储，使用标准配置选择');
                result.configUsed = this.autoSelectConfig();
            }

            // 创建存储服务
            result.storageService = await this.createStorageService(result.configUsed);
            result.success = true;

            return result;

        } catch (error) {
            console.error('❌ 创建存储服务失败:', error);
            result.error = error.message;
            return result;
        }
    }

    autoSelectConfig() {
        return this.config.autoSelectConfig();
    }

    getRecommendations() {
        return this.config.getRecommendations();
    }

    displayConfigInfo(configName) {
        return this.config.displayConfigInfo(configName);
    }

    /**
     * 获取当前存储服务
     * @returns {Object|null} 当前存储服务实例
     */
    getCurrentStorageService() {
        return this.currentStorageService;
    }

    /**
     * 获取当前配置信息
     * @returns {Object|null} 当前配置对象
     */
    getCurrentConfig() {
        if (!this.currentConfigName) {
            return null;
        }
        return this.config.getConfig(this.currentConfigName);
    }

    /**
     * 获取当前配置名称
     * @returns {string|null} 当前配置名称
     */
    getCurrentConfigName() {
        return this.currentConfigName;
    }

    /**
     * 检查存储服务是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isStorageServiceInitialized() {
        return this.isInitialized && this.currentStorageService !== null;
    }

    /**
     * 检查当前配置是否支持云存储
     * @returns {boolean} 是否支持云存储
     */
    isCloudStorageEnabled() {
        // 首先检查是否有 EdgeOne 云存储
        if (this.isEdgeOneStorageBasicallyAvailable()) {
            return true;
        }

        const currentConfig = this.getCurrentConfig();
        if (!currentConfig) {
            return false;
        }

        return currentConfig.enableCloudSync ||
               (currentConfig.cloudConfig && currentConfig.cloudConfig.type) ||
               (currentConfig.adapters && currentConfig.adapters.some(adapter =>
                   adapter.type === 'firebase' ||
                   adapter.type === 'user-cloud' ||
                   adapter.type === 'hybrid'
               ));
    }

    /**
     * 健壮的 EdgeOne 云存储可用性检查
     * @param {boolean} performConnectivityTest - 是否执行连接测试
     * @returns {Promise<Object>} 详细的可用性检查结果
     */
    async isEdgeOneStorageAvailable(performConnectivityTest = false) {
        const result = {
            available: false,
            reason: '',
            details: {},
            timestamp: Date.now()
        };

        try {
            // 1. 基础环境检查
            if (typeof window === 'undefined') {
                result.reason = '非浏览器环境';
                return result;
            }

            // 2. 实例存在性检查
            if (!window.edgeOneStorage) {
                result.reason = 'window.edgeOneStorage 不存在';
                return result;
            }

            // 3. 类型验证
            if (typeof window.edgeOneStorage !== 'object' || window.edgeOneStorage === null) {
                result.reason = 'window.edgeOneStorage 不是有效对象';
                return result;
            }

            // 4. 必要方法检查
            const requiredMethods = ['put', 'get', 'delete'];
            const missingMethods = [];

            for (const method of requiredMethods) {
                if (typeof window.edgeOneStorage[method] !== 'function') {
                    missingMethods.push(method);
                }
            }

            if (missingMethods.length > 0) {
                result.reason = `缺少必要方法: ${missingMethods.join(', ')}`;
                result.details.missingMethods = missingMethods;
                return result;
            }

            // 5. 可选方法检查
            const optionalMethods = ['list', 'clear', 'init', 'test', 'getInfo', 'getStats'];
            const availableMethods = {};

            for (const method of [...requiredMethods, ...optionalMethods]) {
                availableMethods[method] = typeof window.edgeOneStorage[method] === 'function';
            }

            result.details.methods = availableMethods;

            // 6. 初始化状态检查
            if (typeof window.edgeOneStorage.getStats === 'function') {
                try {
                    const stats = window.edgeOneStorage.getStats();
                    result.details.stats = stats;

                    // 检查是否已初始化
                    if (stats && typeof stats.isAvailable === 'boolean') {
                        if (!stats.isAvailable) {
                            result.reason = 'EdgeOne 存储实例不可用（根据内部状态）';
                            result.details.internalStatus = 'unavailable';
                            return result;
                        }
                    }
                } catch (error) {
                    result.details.statsError = error.message;
                }
            }

            // 7. 连接测试（可选）
            if (performConnectivityTest) {
                try {
                    const testKey = `__availability_test_${Date.now()}`;
                    const testValue = 'test';

                    // 测试写入
                    await window.edgeOneStorage.put(testKey, testValue);

                    // 测试读取
                    const retrievedValue = await window.edgeOneStorage.get(testKey);

                    // 测试删除
                    await window.edgeOneStorage.delete(testKey);

                    // 验证数据一致性
                    if (retrievedValue !== testValue) {
                        result.reason = '连接测试失败：数据不一致';
                        result.details.connectivityTest = 'data_mismatch';
                        return result;
                    }

                    result.details.connectivityTest = 'passed';
                } catch (error) {
                    result.reason = `连接测试失败: ${error.message}`;
                    result.details.connectivityTest = 'failed';
                    result.details.connectivityError = error.message;
                    return result;
                }
            }

            // 8. 所有检查通过
            result.available = true;
            result.reason = '所有检查通过';

            return result;

        } catch (error) {
            result.reason = `检查过程中发生异常: ${error.message}`;
            result.details.exception = error.message;
            return result;
        }
    }

    /**
     * 简化的同步 EdgeOne 存储可用性检查
     * @returns {boolean} EdgeOne 云存储是否基本可用
     */
    isEdgeOneStorageBasicallyAvailable() {
        try {
            return typeof window !== 'undefined' &&
                   window.edgeOneStorage &&
                   typeof window.edgeOneStorage === 'object' &&
                   typeof window.edgeOneStorage.put === 'function' &&
                   typeof window.edgeOneStorage.get === 'function' &&
                   typeof window.edgeOneStorage.delete === 'function';
        } catch (error) {
            console.warn('⚠️ EdgeOne 存储基础检查失败:', error);
            return false;
        }
    }

    /**
     * 获取 EdgeOne 云存储信息（同步版本）
     * @returns {Object|null} EdgeOne 云存储信息
     */
    getEdgeOneStorageInfo() {
        if (!this.isEdgeOneStorageBasicallyAvailable()) {
            return null;
        }

        const info = {
            available: true,
            basicCheckPassed: true,
            methods: {
                // 必要方法
                put: typeof window.edgeOneStorage.put === 'function',
                get: typeof window.edgeOneStorage.get === 'function',
                delete: typeof window.edgeOneStorage.delete === 'function',

                // 可选方法
                list: typeof window.edgeOneStorage.list === 'function',
                clear: typeof window.edgeOneStorage.clear === 'function',
                init: typeof window.edgeOneStorage.init === 'function',
                test: typeof window.edgeOneStorage.test === 'function',
                getInfo: typeof window.edgeOneStorage.getInfo === 'function',
                getStats: typeof window.edgeOneStorage.getStats === 'function',

                // 兼容 localStorage 风格的 API
                setItem: typeof window.edgeOneStorage.setItem === 'function',
                getItem: typeof window.edgeOneStorage.getItem === 'function',
                removeItem: typeof window.edgeOneStorage.removeItem === 'function',
                keys: typeof window.edgeOneStorage.keys === 'function'
            }
        };

        // 统计可用方法数量
        const methodCounts = {
            required: 0,
            optional: 0,
            localStorage: 0
        };

        const requiredMethods = ['put', 'get', 'delete'];
        const optionalMethods = ['list', 'clear', 'init', 'test', 'getInfo', 'getStats'];
        const localStorageMethods = ['setItem', 'getItem', 'removeItem', 'keys'];

        requiredMethods.forEach(method => {
            if (info.methods[method]) methodCounts.required++;
        });

        optionalMethods.forEach(method => {
            if (info.methods[method]) methodCounts.optional++;
        });

        localStorageMethods.forEach(method => {
            if (info.methods[method]) methodCounts.localStorage++;
        });

        info.methodCounts = methodCounts;
        info.isFullyCompatible = methodCounts.required === requiredMethods.length;

        // 如果有 getInfo 方法，获取详细信息
        if (info.methods.getInfo) {
            try {
                info.details = window.edgeOneStorage.getInfo();
            } catch (error) {
                console.warn('⚠️ 获取 EdgeOne 存储详细信息失败:', error);
                info.getInfoError = error.message;
            }
        }

        // 如果有 getStats 方法，获取状态信息
        if (info.methods.getStats) {
            try {
                info.stats = window.edgeOneStorage.getStats();
            } catch (error) {
                console.warn('⚠️ 获取 EdgeOne 存储状态失败:', error);
                info.getStatsError = error.message;
            }
        }

        return info;
    }

    /**
     * 获取 EdgeOne 云存储详细信息（异步版本，包含连接测试）
     * @param {boolean} includeConnectivityTest - 是否包含连接测试
     * @returns {Promise<Object|null>} EdgeOne 云存储详细信息
     */
    async getEdgeOneStorageDetailedInfo(includeConnectivityTest = false) {
        const availabilityResult = await this.isEdgeOneStorageAvailable(includeConnectivityTest);

        if (!availabilityResult.available) {
            return {
                available: false,
                reason: availabilityResult.reason,
                details: availabilityResult.details,
                timestamp: availabilityResult.timestamp
            };
        }

        // 合并基础信息和详细检查结果
        const basicInfo = this.getEdgeOneStorageInfo();

        return {
            ...basicInfo,
            detailedCheckPassed: true,
            availabilityCheck: availabilityResult,
            timestamp: availabilityResult.timestamp
        };
    }

    /**
     * 重置存储服务
     */
    resetStorageService() {
        if (this.currentStorageService && typeof this.currentStorageService.destroy === 'function') {
            this.currentStorageService.destroy();
        }

        this.currentStorageService = null;
        this.currentConfigName = null;
        this.isInitialized = false;

        console.log('🔄 存储服务已重置');
    }

    /**
     * 切换存储配置
     * @param {string} configName 新的配置名称
     * @returns {Promise<Object>} 新的存储服务实例
     */
    async switchStorageConfig(configName) {
        console.log(`🔄 切换存储配置: ${this.currentConfigName} → ${configName}`);

        // 重置当前服务
        this.resetStorageService();

        // 创建新的存储服务
        return await this.createStorageService(configName);
    }
}

// 导出配置类
if (typeof window !== 'undefined') {
    window.StorageConfig = StorageConfig;
    window.StorageConfigManager = StorageConfigManager;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        StorageConfig,
        StorageConfigManager
    };
}
